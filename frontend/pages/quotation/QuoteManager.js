import React, { useState, useEffect } from 'react';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import BackButton from '@/components/BackButton';
import Modal from '@/components/Modal';
import ScrollToTopButton from '@/components/scrollup';
import { motion } from 'framer-motion';
import { useRouter } from 'next/router';
import {
  FormSkeleton,
  TableSkeleton,
  CardSkeleton,
} from '@/components/skeleton';

const QuoteManager = () => {
  const router = useRouter();
  const { projectId: queryProjectId, selectedItems: selectedItemsStr } =
    router.query;
  const [projectId, setProjectId] = useState(queryProjectId || '');
  const [projects, setProjects] = useState([]);
  const [customer, setCustomer] = useState(null);
  const [categories, setCategories] = useState([]);
  const [itemsByCategory, setItemsByCategory] = useState({});
  const [selectedItems, setSelectedItems] = useState({});
  const [labourCost, setLabourCost] = useState(0);
  const [labourDescription, setLabourDescription] = useState('');
  const [otherItems, setOtherItems] = useState([]);
  const [totalCost, setTotalCost] = useState(0);
  const [isLoading, setIsLoading] = useState(true); // Set to true initially
  const [generatedQuote, setGeneratedQuote] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [showLabourForm, setShowLabourForm] = useState(false);
  const [showOtherItemForm, setShowOtherItemForm] = useState(false);
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);
  const [selectedCategoryForDownload, setSelectedCategoryForDownload] =
    useState(null);
  const [newOtherItem, setNewOtherItem] = useState({
    name: '',
    quantity: 1,
    cost: 0,
  });

  // Parse selectedItems from URL when component mounts or query changes
  useEffect(() => {
    if (selectedItemsStr) {
      try {
        const decoded = decodeURIComponent(selectedItemsStr);
        const parsed = JSON.parse(decoded);

        // Don't clean the data - preserve all properties including printSeparately
        setSelectedItems(parsed);
      } catch (error) {
        console.error('Error parsing selectedItems:', error);
      }
    }
  }, [selectedItemsStr]);

  useEffect(() => {
    setIsLoading(true);
    fetch('/api/projects')
      .then(response => response.json())
      .then(data => {
        setProjects(data);
        setIsLoading(false);
      })
      .catch(err => {
        console.error('Error fetching projects:', err);
        setIsLoading(false);
      });
  }, []);

  useEffect(() => {
    if (projectId) {
      setIsLoading(true);
      Promise.all([
        fetch(`/api/customer/${projectId}`).then(res => res.json()),
        fetch('/api/category').then(res => res.json()),
      ])
        .then(([customerRes, categoriesRes]) => {
          setCustomer(customerRes);
          setCategories(categoriesRes);
        })
        .catch(err => {
          console.error('Error fetching data:', err);
        })
        .finally(() => setIsLoading(false));
    }
  }, [projectId]);

  useEffect(() => {
    if (categories.length > 0) {
      setIsLoading(true);
      const fetchPromises = categories.map(category =>
        fetch(`/api/items/${category.category_id}`)
          .then(res => {
            // Check if the response is ok (status in the range 200-299)
            if (!res.ok) {
              // If response is not ok, return an empty array instead of throwing an error
              console.warn(
                `No items found for category ${category.category_id}`
              );
              return [];
            }
            return res.json();
          })
          .then(data => {
            setItemsByCategory(prev => ({
              ...prev,
              [category.category_id]: data,
            }));
            return data;
          })
          .catch(err => {
            console.error(
              `Error fetching items for category ${category.category_name}:`,
              err
            );
            // Return an empty array on error to prevent further errors
            return [];
          })
      );

      Promise.all(fetchPromises)
        .then(() => setIsLoading(false))
        .catch(() => setIsLoading(false));
    }
  }, [categories]);

  // In the QuoteManager component, update the useEffect for total cost calculation
  useEffect(() => {
    // Calculate category totals (excluding printSeparately items)
    const categoryTotals = categories.reduce((acc, category) => {
      const items = selectedItems[category.category_id] || [];
      acc[category.category_name] = items.reduce((sum, item) => {
        if (!item.printSeparately) {
          return sum + item.cost * item.quantity;
        }
        return sum;
      }, 0);
      return acc;
    }, {});

    // Calculate separate items total
    const separateItemsTotal = categories.reduce((sum, category) => {
      const items = selectedItems[category.category_id] || [];
      return (
        sum +
        items.reduce((catSum, item) => {
          if (item.printSeparately) {
            return catSum + item.cost * item.quantity;
          }
          return catSum;
        }, 0)
      );
    }, 0);

    // Calculate labour cost
    const labourTotal = parseFloat(labourCost) || 0;

    // Calculate miscellaneous items total
    const miscellaneousTotal = otherItems.reduce(
      (sum, item) => sum + item.cost * item.quantity,
      0
    );

    // Calculate total cost (include all items)
    const total =
      Object.values(categoryTotals).reduce((sum, cost) => sum + cost, 0) +
      separateItemsTotal +
      labourTotal +
      miscellaneousTotal;

    setTotalCost(total);
  }, [selectedItems, labourCost, otherItems, categories]);

  const getSelectedItemsForCategory = categoryId => {
    return selectedItems[categoryId] || [];
  };

  const getCategoryTotal = categoryId => {
    const items = selectedItems[categoryId] || [];
    return items.reduce((sum, item) => {
      if (!item.printSeparately) {
        return sum + item.cost * item.quantity;
      }
      return sum;
    }, 0);
  };

  const navigateToCategoryItems = categoryId => {
    router.push({
      pathname: '/quotation/category-items',
      query: {
        categoryId,
        projectId,
        customer: JSON.stringify(customer),
        selectedItems: encodeURIComponent(JSON.stringify(selectedItems)),
      },
    });
  };

  const addLabourCost = () => {
    setShowLabourForm(false);
  };

  const addOtherItem = () => {
    if (newOtherItem.name && newOtherItem.cost > 0) {
      setOtherItems([
        ...otherItems,
        {
          ...newOtherItem,
          cost: parseFloat(newOtherItem.cost),
          quantity: parseInt(newOtherItem.quantity) || 1,
        },
      ]);
      setNewOtherItem({ name: '', quantity: 1, cost: 0 });
      setShowOtherItemForm(false);
    }
  };

  const removeOtherItem = index => {
    setOtherItems(otherItems.filter((_, i) => i !== index));
  };

  const fetchLastQuoteId = async () => {
    try {
      const res = await fetch('/api/last-quote-id');
      const data = await res.json();
      return data.nextQuoteId;
    } catch (err) {
      console.error('Error fetching last quote ID:', err);
      return null;
    }
  };

  const handleDownloadClick = React.useCallback(categoryId => {
    setSelectedCategoryForDownload(categoryId);
    setShowDownloadOptions(true);
  }, []);

  const generateDripPlumbingPDF = option => {
    const doc = new jsPDF();
    let title = '';
    let tableData = [];

    // Add headers
    tableData.push([
      { content: 'Item Name', styles: { fontStyle: 'bold' } },
      { content: 'Quantity', styles: { fontStyle: 'bold' } },
    ]);

    if (option === 'combined') {
      title = 'Drip & Plumbing Items List';

      // Find both categories
      const dripCategory = categories.find(cat => cat.category_name === 'Drip');
      const plumbingCategory = categories.find(
        cat => cat.category_name === 'Plumbing'
      );

      // Add Drip items
      if (dripCategory) {
        tableData.push([
          {
            content: 'Drip Items',
            colSpan: 2,
            styles: {
              fillColor: [41, 128, 185],
              textColor: 255,
              fontStyle: 'bold',
            },
          },
        ]);

        const dripItems = selectedItems[dripCategory.category_id] || [];
        dripItems.forEach(item => {
          const itemDetails = itemsByCategory[dripCategory.category_id]?.find(
            i => i.item_id === item.item_id
          );
          tableData.push([
            itemDetails?.item_name || 'Unknown Item',
            item.quantity,
          ]);
        });
      }

      // Add Plumbing items
      if (plumbingCategory) {
        tableData.push([
          {
            content: 'Plumbing Items',
            colSpan: 2,
            styles: {
              fillColor: [41, 128, 185],
              textColor: 255,
              fontStyle: 'bold',
            },
          },
        ]);

        const plumbingItems = selectedItems[plumbingCategory.category_id] || [];
        plumbingItems.forEach(item => {
          const itemDetails = itemsByCategory[
            plumbingCategory.category_id
          ]?.find(i => i.item_id === item.item_id);
          tableData.push([
            itemDetails?.item_name || 'Unknown Item',
            item.quantity,
          ]);
        });
      }
    } else {
      // Single category download
      const category = categories.find(
        cat =>
          cat.category_id.toString() === selectedCategoryForDownload.toString()
      );
      if (category) {
        title = `${category.category_name} Items List`;

        tableData.push([
          {
            content: `${category.category_name} Items`,
            colSpan: 2,
            styles: {
              fillColor: [41, 128, 185],
              textColor: 255,
              fontStyle: 'bold',
            },
          },
        ]);

        const items = selectedItems[category.category_id] || [];
        items.forEach(item => {
          const itemDetails = itemsByCategory[category.category_id]?.find(
            i => i.item_id === item.item_id
          );
          tableData.push([
            itemDetails?.item_name || 'Unknown Item',
            item.quantity,
          ]);
        });
      }
    }

    // Add title
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(16);
    doc.text(title, 105, 20, { align: 'center' });

    // Create the table
    autoTable(doc, {
      body: tableData,
      startY: 30,
      theme: 'grid',
      headStyles: {
        fillColor: [41, 128, 185],
        textColor: 255,
      },
      styles: {
        fontSize: 10,
        cellPadding: 3,
        halign: 'center',
      },
      columnStyles: {
        0: { halign: 'left', cellWidth: 'auto' },
        1: { halign: 'center', cellWidth: 20 },
      },
    });

    // Save the PDF
    doc.save(`${title.replace(/ /g, '_')}.pdf`);
    setShowDownloadOptions(false);
  };
  const generateQuote = async () => {
    setLoading(true);
    const quoteId = await fetchLastQuoteId();
    const quoteDate = new Date().toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    });

    const doc = new jsPDF();
    const logo =
      'data:image/jpeg;base64,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';

    // Add Title and Logo
    doc.setFillColor(173, 216, 230);
    doc.rect(15, 15, 180, 20, 'F');
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(18);
    doc.setTextColor(0, 0, 0);
    doc.text('Ukshati Technologies Pvt Ltd.', 20, 30);
    doc.addImage(logo, 'JPEG', 150, 10, 30, 30);

    // Add Company Details
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    doc.text('2nd floor, Pramod Automobiles bldg.', 20, 43);
    doc.text('Karangalpady', 20, 51);
    doc.text('Mangalore - 575003', 20, 59);
    doc.text('Karnataka', 20, 67);
    doc.text('Phone: + 91 8861567365', 20, 75);
    doc.textWithLink('www.ukshati.com', 20, 83, {
      url: 'http://www.ukshati.com',
    });

    // Draw a line
    doc.setDrawColor(0, 0, 139);
    doc.setLineWidth(1);
    doc.line(20, 88, 190, 88);

    // Add Quote ID and Customer Info
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(12);
    doc.text(
      `Quote ID: ${quoteId}                                                Date: ${quoteDate}`,
      20,
      100
    );
    doc.text(
      `Customer: ${customer?.customer_name}                          Address: ${customer?.address}`,
      20,
      108
    );
    doc.text(`Phone: ${customer?.cphone}`, 20, 116);

    // Prepare table data with optimized columns
    const tableData = [];
    const columnStyles = {
      0: { cellWidth: 90 }, // Reduced from 110 to 90
      1: { cellWidth: 20, halign: 'center' }, // Reduced from 20 to 15
      2: { cellWidth: 35, halign: 'center' }, // Reduced from 30 to 25
      3: { cellWidth: 35, halign: 'center' }, // Reduced from 25 to 20
    };

    // Add column headers
    tableData.push([
      { content: 'Description', styles: { fontStyle: 'bold' } },
      { content: 'Qty', styles: { fontStyle: 'bold', halign: 'center' } },
      {
        content: 'Unit Price',
        styles: { fontStyle: 'bold', halign: 'center' },
      },
      { content: 'Total', styles: { fontStyle: 'bold', halign: 'center' } },
    ]);

    // Process all categories in a single flow
    // In the generateQuote function, modify the table data generation:

    // 1. First process all regular categories (non-Drip, non-Plumbing)
    // 1. First process all regular categories (non-Drip, non-Plumbing)
    categories.forEach(category => {
      const items = selectedItems[category.category_id] || [];
      const regularItems = items.filter(item => !item.printSeparately);

      if (regularItems.length === 0) return;

      // For Automation and Others, add detailed items
      if (['Automation', 'Others'].includes(category.category_name)) {
        tableData.push([
          {
            content: category.category_name,
            colSpan: 4,
            styles: {
              fillColor: [41, 128, 185],
              textColor: 255,
              fontStyle: 'bold',
            },
          },
        ]);

        regularItems.forEach(item => {
          const itemDetails = itemsByCategory[category.category_id]?.find(
            i => i.item_id === item.item_id
          );
          tableData.push([
            itemDetails?.item_name.substring(0, 50) || 'Unknown Item',
            item.quantity,
            `Rs ${item.cost.toFixed(2)}`,
            `Rs ${(item.cost * item.quantity).toFixed(2)}`,
          ]);
        });
      } else {
        // For other categories, add as single line
        const total = regularItems.reduce(
          (sum, item) => sum + item.cost * item.quantity,
          0
        );
        if (total > 0) {
          tableData.push([
            category.category_name,
            '1',
            `Rs ${total.toFixed(2)}`,
            `Rs ${total.toFixed(2)}`,
          ]);
        }
      }
    });

    // Add labour cost if exists
    if (labourCost > 0) {
      tableData.push([
        'Labour Charges',
        '1',
        `Rs ${parseFloat(labourCost).toFixed(2)}`,
        `Rs ${parseFloat(labourCost).toFixed(2)}`,
      ]);
    }

    // Now add all separate items under a special section
    const allSeparateItems = [];
    categories.forEach(category => {
      const items = selectedItems[category.category_id] || [];
      items.forEach(item => {
        if (item.printSeparately) {
          const itemDetails = itemsByCategory[category.category_id]?.find(
            i => i.item_id === item.item_id
          );
          if (itemDetails) {
            allSeparateItems.push({
              ...item,
              name: itemDetails.item_name,
              category: category.category_name,
            });
          }
        }
      });
    });

    if (allSeparateItems.length > 0) {
      tableData.push([
        {
          content: 'Additional Items (Printed Separately)',
          colSpan: 4,
          styles: {
            fillColor: [255, 153, 0],
            textColor: 255,
            fontStyle: 'bold',
          },
        },
      ]);

      allSeparateItems.forEach(item => {
        tableData.push([
          `${item.name} (${item.category})`,
          item.quantity,
          `Rs ${item.cost.toFixed(2)}`,
          `Rs ${(item.cost * item.quantity).toFixed(2)}`,
        ]);
      });
    }

    // Add other miscellaneous items (if any)
    if (otherItems.length > 0) {
      tableData.push([
        {
          content: 'Other Items',
          colSpan: 4,
          styles: {
            fillColor: [41, 128, 185],
            textColor: 255,
            fontStyle: 'bold',
          },
        },
      ]);

      otherItems.forEach(item => {
        tableData.push([
          item.name.substring(0, 50),
          item.quantity,
          `Rs ${item.cost.toFixed(2)}`,
          `Rs ${(item.cost * item.quantity).toFixed(2)}`,
        ]);
      });
    }

    // Add total
    tableData.push([
      {
        content: 'TOTAL',
        colSpan: 3,
        styles: {
          fontStyle: 'bold',
          fillColor: [255, 204, 0],
          lineWidth: 0.2,
          lineColor: [0, 0, 0],
        },
      },
      {
        content: `Rs ${totalCost.toFixed(2)}`,
        styles: {
          fontStyle: 'bold',
          fillColor: [255, 204, 0],
          lineWidth: 0.2,
          lineColor: [0, 0, 0],
        },
      },
    ]);

    // Create the table
    autoTable(doc, {
      body: tableData,
      startY: 125,
      margin: { left: 15, right: 15 }, // Reduced margins to fit content better
      theme: 'grid',
      styles: {
        fontSize: 9, // Slightly smaller font
        cellPadding: 2, // Reduced padding
        halign: 'left',
        valign: 'middle',
        lineWidth: 0.1, // Thinner grid lines
      },
      columnStyles: columnStyles,
      tableWidth: 'wrap',
      pageBreak: 'auto',
      rowPageBreak: 'avoid',
      didParseCell: function (data) {
        // Apply special styling for header rows
        if (
          data.cell.raw &&
          typeof data.cell.raw === 'object' &&
          data.cell.raw.colSpan
        ) {
          data.cell.styles = data.cell.raw.styles || {};
        }
      },
    });

    // Add footer content with proper spacing
    const finalY = Math.max(doc.lastAutoTable.finalY + 10, 200);
    const qrCode =
      'data:image/jpeg;base64,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';
    doc.addImage(qrCode, 'PNG', 160, finalY, 25, 25);

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(8);
    doc.text('Bank Details:', 20, finalY);
    doc.text('ICICI Bank', 20, finalY + 6);
    doc.text('Name: Ukshati Technologies Private Limited', 20, finalY + 12);
    doc.text('Account Number: XXXXXXXXXXXXXXX', 20, finalY + 18);
    doc.text('IFSC Code: XXXXXXXX', 20, finalY + 24);

    const greenLineY = finalY + 30;
    doc.setDrawColor(0, 0, 139);
    doc.setLineWidth(1);
    doc.line(20, greenLineY, 190, greenLineY);

    const specialNotesY = greenLineY + 10;
    doc.text('Special Notes:', 20, specialNotesY);
    doc.text('Two-year warranty included.', 20, specialNotesY + 6);
    doc.text('SIM card not included in the package.', 20, specialNotesY + 12);
    doc.text(
      'Payment terms: 70% advance, 30% after installation.',
      20,
      specialNotesY + 18
    );
    doc.text('Extra work charged separately.', 20, specialNotesY + 24);
    doc.text('Wi-Fi extender extra if signal weak.', 20, specialNotesY + 30);
    doc.text(
      'Quote validity: 6 months from date of issue.',
      20,
      specialNotesY + 36
    );

    const pdfData = doc.output('dataurlstring');
    setGeneratedQuote(pdfData);
    setLoading(false);
  };

  const saveQuote = () => {
    setLoading(true);

    // Calculate category costs
    const categoryCosts = categories.reduce((acc, category) => {
      const items = selectedItems[category.category_id] || [];
      acc[`${category.category_name.toLowerCase()}_cost`] = items.reduce(
        (sum, item) => sum + item.cost * item.quantity,
        0
      );
      return acc;
    }, {});

    // Calculate miscellaneous items total
    const miscellaneousTotal = otherItems.reduce(
      (sum, item) => sum + item.cost * item.quantity,
      0
    );

    fetch('/api/save-quote', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        project_id: projectId,
        customer_name: customer?.customer_name,
        ...categoryCosts,
        labour_cost: parseFloat(labourCost) || 0,
        additional_cost: miscellaneousTotal,
        total_cost: totalCost,
      }),
    })
      .then(res => res.json())
      .then(data => {
        console.log('Quote saved:', data);
        setSuccessMessage('Quote saved successfully!');
        setLoading(false);
      })
      .catch(err => {
        console.error('Error saving quote:', err);
        setLoading(false);
      });
  };

  return (
    <div className="bg-black text-white min-h-screen flex flex-col items-center justify-center relative">
      <ScrollToTopButton />
      <div className="absolute top-4 left-4 z-10">
        <BackButton route="/quotation/home" />
      </div>

      <div className="flex flex-col text-white items-center justify-center w-full px-4 py-20">
        <h1 className="text-4xl font-bold mb-16 mt-8">Quote Management</h1>

        <motion.div
          className="bg-gray-900/80 p-8 rounded-xl shadow-xl w-full max-w-4xl mx-auto border border-gray-800"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {isLoading ? (
            <FormSkeleton rows={3} />
          ) : (
            <div className="text-center mb-8">
              <label className="text-white text-lg mb-2 block">
                Select Project:
              </label>
              <select
                onChange={e => setProjectId(e.target.value)}
                value={projectId}
                className="bg-gray-800 text-white p-3 rounded-lg w-full md:w-2/3 mx-auto border border-gray-700 focus:border-blue-500 focus:outline-none"
              >
                <option value="">-- Select Project --</option>
                {projects.map(proj => (
                  <option key={proj.pid} value={proj.pid}>
                    {proj.pname}
                  </option>
                ))}
              </select>
            </div>
          )}

          {isLoading ? (
            <div className="mb-8">
              <CardSkeleton count={1} />
            </div>
          ) : (
            customer && (
              <div className="mb-8 bg-gray-800/50 p-4 rounded-lg border border-gray-700 text-center">
                <h3 className="text-lg font-semibold mb-2 text-blue-400">
                  Customer Information
                </h3>
                <p className="text-white">
                  <strong>Name:</strong> {customer.customer_name}
                </p>
                <p className="text-white">
                  <strong>Address:</strong> {customer.address}
                </p>
                <p className="text-white">
                  <strong>Phone:</strong> {customer.cphone}
                </p>
              </div>
            )
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {isLoading ? (
              <CardSkeleton count={6} />
            ) : (
              categories.map(cat => {
                // Skip Labour category from the grid
                if (cat.category_name === 'Labour') return null;

                const selectedItemsForCategory = getSelectedItemsForCategory(
                  cat.category_id
                );

                return (
                  <motion.div
                    key={cat.category_id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex justify-between items-center mb-1">
                      <button
                        onClick={() => navigateToCategoryItems(cat.category_id)}
                        className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg w-full text-left flex justify-between items-center"
                      >
                        <span>{cat.category_name}</span>
                        <span>→</span>
                      </button>

                      {/* Add download button for Drip and Plumbing */}
                      {(cat.category_name === 'Drip' ||
                        cat.category_name === 'Plumbing') &&
                        selectedItemsForCategory.length > 0 && (
                          <button
                            onClick={() => handleDownloadClick(cat.category_id)}
                            className="ml-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                            title="Download Items List"
                          >
                            ↓
                          </button>
                        )}
                    </div>

                    {selectedItemsForCategory.length > 0 && (
                      <div className="mt-2 bg-gray-800 p-3 rounded-lg">
                        <h4 className="font-bold text-white mb-2">
                          Selected Items:
                        </h4>
                        <ul className="space-y-2">
                          {selectedItemsForCategory.map(item => {
                            const itemDetails = itemsByCategory[
                              cat.category_id
                            ]?.find(i => i.item_id === item.item_id);
                            return (
                              <li
                                key={item.item_id}
                                className={`flex justify-between ${item.printSeparately ? 'border-l-4 border-yellow-500 pl-2' : ''}`}
                              >
                                <span className="text-white">
                                  {itemDetails?.item_name || 'Unknown Item'}
                                  {item.printSeparately &&
                                    ' (Print Separately)'}
                                  (Qty: {item.quantity})
                                </span>
                                <span className="text-green-400">
                                  Rs {(item.cost * item.quantity).toFixed(2)}
                                </span>
                              </li>
                            );
                          })}
                        </ul>
                        <div className="mt-2 text-right font-bold text-white">
                          Category Subtotal: Rs{' '}
                          {getCategoryTotal(cat.category_id).toFixed(2)}
                        </div>
                        {selectedItemsForCategory.some(
                          item => item.printSeparately
                        ) && (
                          <div className="mt-1 text-right text-yellow-400">
                            (Excluding{' '}
                            {
                              selectedItemsForCategory.filter(
                                item => item.printSeparately
                              ).length
                            }
                            separate items)
                          </div>
                        )}
                      </div>
                    )}
                  </motion.div>
                );
              })
            )}
          </div>

          {isLoading ? (
            <div className="mb-6">
              <FormSkeleton rows={2} />
            </div>
          ) : (
            projectId && (
              <>
                {/* Labour Cost Section */}
                <div className="mb-6 bg-gray-800 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-white font-bold">Labour Cost</h3>
                    {!showLabourForm ? (
                      <button
                        onClick={() => setShowLabourForm(true)}
                        className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                      >
                        +
                      </button>
                    ) : (
                      <button
                        onClick={() => setShowLabourForm(false)}
                        className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                      >
                        ×
                      </button>
                    )}
                  </div>

                  {showLabourForm ? (
                    <div className="space-y-3">
                      <div>
                        <label className="text-white block mb-1">
                          Description:
                        </label>
                        <input
                          type="text"
                          value={labourDescription}
                          onChange={e => setLabourDescription(e.target.value)}
                          className="bg-gray-700 text-white p-2 rounded w-full"
                          placeholder="Labour description"
                        />
                      </div>
                      <div>
                        <label className="text-white block mb-1">Cost:</label>
                        <input
                          type="number"
                          value={labourCost}
                          onChange={e => setLabourCost(e.target.value)}
                          className="bg-gray-700 text-white p-2 rounded w-full"
                          placeholder="Enter labour cost"
                        />
                      </div>
                      <button
                        onClick={addLabourCost}
                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                      >
                        Add Labour Cost
                      </button>
                    </div>
                  ) : (
                    <div className="text-white">
                      {labourCost > 0 ? (
                        <div className="flex justify-between items-center">
                          <span>
                            {labourDescription || 'Labour Cost'}: Rs{' '}
                            {parseFloat(labourCost).toFixed(2)}
                          </span>
                        </div>
                      ) : (
                        <p>No labour cost added</p>
                      )}
                    </div>
                  )}
                </div>

                {/* Other Items Section */}
                <div className="mb-6 bg-gray-800 p-4 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-white font-bold">Additional Items</h3>
                    {!showOtherItemForm ? (
                      <button
                        onClick={() => setShowOtherItemForm(true)}
                        className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                      >
                        +
                      </button>
                    ) : (
                      <button
                        onClick={() => setShowOtherItemForm(false)}
                        className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                      >
                        ×
                      </button>
                    )}
                  </div>

                  {showOtherItemForm ? (
                    <div className="space-y-3">
                      <div>
                        <label className="text-white block mb-1">
                          Item Name:
                        </label>
                        <input
                          type="text"
                          value={newOtherItem.name}
                          onChange={e =>
                            setNewOtherItem({
                              ...newOtherItem,
                              name: e.target.value,
                            })
                          }
                          className="bg-gray-700 text-white p-2 rounded w-full"
                          placeholder="Item name"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="text-white block mb-1">
                            Quantity:
                          </label>
                          <input
                            type="number"
                            min="1"
                            value={newOtherItem.quantity}
                            onChange={e =>
                              setNewOtherItem({
                                ...newOtherItem,
                                quantity: e.target.value,
                              })
                            }
                            className="bg-gray-700 text-white p-2 rounded w-full"
                          />
                        </div>
                        <div>
                          <label className="text-white block mb-1">Cost:</label>
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={newOtherItem.cost}
                            onChange={e =>
                              setNewOtherItem({
                                ...newOtherItem,
                                cost: e.target.value,
                              })
                            }
                            className="bg-gray-700 text-white p-2 rounded w-full"
                            placeholder="Enter cost"
                          />
                        </div>
                      </div>
                      <button
                        onClick={addOtherItem}
                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                      >
                        Add Item
                      </button>
                    </div>
                  ) : null}

                  {otherItems.length > 0 && (
                    <div className="mt-4 space-y-2">
                      {otherItems.map((item, index) => (
                        <div
                          key={index}
                          className="flex justify-between items-center bg-gray-700 p-2 rounded"
                        >
                          <div>
                            <span className="text-white">{item.name}</span>
                            <span className="text-gray-400 text-sm ml-2">
                              (Qty: {item.quantity} @ Rs {item.cost.toFixed(2)}
                              /unit)
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-green-400 mr-2">
                              Rs {(item.cost * item.quantity).toFixed(2)}
                            </span>
                            <button
                              onClick={() => removeOtherItem(index)}
                              className="text-red-500 hover:text-red-400"
                            >
                              ×
                            </button>
                          </div>
                        </div>
                      ))}
                      <div className="text-right font-bold text-white mt-2">
                        Additional Items Total: Rs{' '}
                        {otherItems
                          .reduce(
                            (sum, item) => sum + item.cost * item.quantity,
                            0
                          )
                          .toFixed(2)}
                      </div>
                    </div>
                  )}
                </div>
              </>
            )
          )}

          {isLoading ? (
            <div className="mb-8">
              <CardSkeleton count={1} />
            </div>
          ) : (
            projectId && (
              <>
                <div className="text-2xl font-bold text-white mb-8 text-center bg-gray-800/50 p-4 rounded-lg border border-gray-700">
                  Total Cost:{' '}
                  <span className="text-green-400">
                    Rs {totalCost.toFixed(2)}
                  </span>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                  <motion.button
                    onClick={saveQuote}
                    className="px-8 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg shadow-lg hover:from-green-700 hover:to-green-800 transition-colors font-semibold"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {loading ? 'Saving...' : 'Save Quote'}
                  </motion.button>

                  {successMessage && (
                    <div className="text-green-500 flex items-center bg-green-900/20 px-4 py-2 rounded-lg">
                      ✓ {successMessage}
                    </div>
                  )}

                  <motion.button
                    onClick={generateQuote}
                    className="px-8 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg shadow-lg hover:from-blue-700 hover:to-blue-800 transition-colors font-semibold"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Generate Quote
                  </motion.button>
                </div>
              </>
            )
          )}
        </motion.div>

        {/* Other existing code... */}

        {/* Add this Modal just before the generatedQuote section */}
        <Modal
          isOpen={showDownloadOptions}
          onClose={() => setShowDownloadOptions(false)}
          title="Download Options"
          preventFlicker={true}
          className="z-[1000]" // High z-index to ensure it appears above everything
        >
          <div className="space-y-4 p-4">
            <button
              onClick={() => generateDripPlumbingPDF('single')}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Download{' '}
              {
                categories.find(
                  c =>
                    c.category_id.toString() ===
                    selectedCategoryForDownload?.toString()
                )?.category_name
              }{' '}
              Only
            </button>

            <button
              onClick={() => generateDripPlumbingPDF('combined')}
              className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Download Combined Drip & Plumbing
            </button>

            <button
              onClick={() => setShowDownloadOptions(false)}
              className="w-full px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Cancel
            </button>
          </div>
        </Modal>

        {loading && !generatedQuote ? (
          <div className="mt-12 w-full max-w-4xl mx-auto">
            <h3 className="text-white text-2xl mb-6 text-center font-bold">
              Generating Quote...
            </h3>
            <div className="border border-gray-700 rounded-xl shadow-xl p-8 bg-gray-900/80">
              <CardSkeleton count={4} />
            </div>
          </div>
        ) : (
          generatedQuote && (
            <motion.div
              className="mt-12 w-full max-w-4xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h3 className="text-white text-2xl mb-6 text-center font-bold">
                Generated Quote
              </h3>
              <iframe
                src={generatedQuote}
                width="100%"
                height="700px"
                className="border border-gray-700 rounded-xl shadow-xl"
                title="Generated Quote"
              />
            </motion.div>
          )
        )}
      </div>
    </div>
  );
};

export default QuoteManager;
