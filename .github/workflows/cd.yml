name: 🚀 CD Pipeline

on:
  workflow_run:
    workflows: ["🔄 CI Pipeline"]
    types:
      - completed
    branches:
      - main  # ✅ Runs only when CI succeeds on the main branch

jobs:
  deploy:
    name: 🚀 Deploy
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}  # ✅ Only runs if CI passed

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔧 Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: � Setup SSH Key for Raspberry Pi
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.PI_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.PI_HOST }} >> ~/.ssh/known_hosts

      - name: �🔐 Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: 🏗️ Build Multi-Architecture Images
        run: |
          echo "🐳 Building multi-arch frontend image (x86_64 + ARM64)..."
          docker buildx build --platform linux/amd64,linux/arm64 \
            -t ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-frontend:${{ github.sha }} \
            -t ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-frontend:latest \
            --push .

          echo "🐳 Building multi-arch MySQL image (x86_64 + ARM64)..."
          docker buildx build --platform linux/amd64,linux/arm64 \
            -f Dockerfile.mysql \
            -t ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-database:${{ github.sha }} \
            -t ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-database:latest \
            --push .

      - name: 🚀 Deploy to Raspberry Pi
        run: |
          echo "🍓 Deploying to Raspberry Pi..."

          # Create deployment script
          cat > deploy.sh << 'EOF'
          #!/bin/bash
          echo "🔄 Stopping existing containers..."
          docker-compose down || true

          echo "🧹 Cleaning up old images..."
          docker image prune -f

          echo "📥 Pulling latest images..."
          docker pull ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-frontend:${{ github.sha }}
          docker pull ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-database:${{ github.sha }}

          echo "🔄 Updating docker-compose.yml with new image tags..."
          sed -i 's|image: .*ukshati-frontend.*|image: ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-frontend:${{ github.sha }}|g' docker-compose.yml
          sed -i 's|image: .*ukshati-database.*|image: ${{ secrets.DOCKERHUB_USERNAME }}/ukshati-database:${{ github.sha }}|g' docker-compose.yml

          echo "🚀 Starting updated containers..."
          docker-compose up -d

          echo "⏳ Waiting for services to be ready..."
          sleep 30

          echo "🔍 Checking service health..."
          docker-compose ps

          echo "✅ Deployment completed!"
          EOF

          # Copy and execute deployment script on Raspberry Pi
          scp -o StrictHostKeyChecking=no deploy.sh ${{ secrets.PI_USER }}@${{ secrets.PI_HOST }}:/home/<USER>/
          ssh -o StrictHostKeyChecking=no ${{ secrets.PI_USER }}@${{ secrets.PI_HOST }} "cd /home/<USER>/ukshati && chmod +x deploy.sh && ./deploy.sh"

      - name: 📢 Notify Success
        if: success()
        run: echo "🎉 Deployment completed successfully!"

      - name: 📢 Notify Failure
        if: failure()
        run: echo "❌ Deployment failed!"